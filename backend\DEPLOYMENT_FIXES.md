# Render.com Deployment Fixes

This document outlines the fixes applied to resolve deployment issues on Render.com.

## Issues Fixed

### 1. Express Rate Limit Trust Proxy Error
**Error:** `ValidationError: The Express 'trust proxy' setting is true, which allows anyone to trivially bypass IP-based rate limiting`

**Fix:** 
- Configured conditional trust proxy settings based on environment
- Added proper key generator for rate limiting that works with <PERSON><PERSON>'s proxy setup
- Set `trust proxy` to `1` in production (trusting only the first proxy)

### 2. Playwright Browser Installation Issues
**Error:** `browserType.launch: Executable doesn't exist at /opt/render/.cache/ms-playwright/chromium_headless_shell-1155/chrome-linux/headless_shell`

**Fix:**
- Updated `render.yaml` to install Playwright browsers with dependencies
- Added proper environment variables for Playwright
- Created startup script to verify browser installation
- Enhanced browser launch options for Render.com environment

### 3. PDF Generation Failures
**Error:** Internal Server Error during PDF generation

**Fix:**
- Added comprehensive browser launch options for headless environments
- Implemented proper error handling and logging
- Added viewport configuration for consistent rendering

## Files Modified

### 1. `index.js`
- Fixed trust proxy configuration
- Enhanced rate limiting setup
- Improved Playwright browser launch options
- Added better error handling and logging

### 2. `render.yaml`
- Updated build command to install Playwright with dependencies
- Added necessary environment variables
- Set proper Node.js version

### 3. `package.json`
- Updated scripts to use startup script
- Enhanced postinstall script for Playwright

### 4. `startup.js` (New)
- Pre-flight checks for Playwright installation
- Automatic browser installation if missing
- Environment-specific configuration

### 5. `Dockerfile` (Updated)
- Upgraded to Node.js 18
- Proper Playwright installation
- Security improvements with non-root user

## Environment Variables Required

Add these to your Render.com service:

```
NODE_ENV=production
RENDER=true
PLAYWRIGHT_BROWSERS_PATH=/opt/render/.cache/ms-playwright
PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=false
```

## Deployment Steps

1. **Push the updated code** to your repository
2. **Redeploy** your Render.com service
3. **Monitor logs** during deployment to ensure Playwright installs correctly
4. **Test PDF generation** endpoints after deployment

## Testing

After deployment, test these endpoints:
- `/api/generate-pdf` (Receipt generation)
- `/api/generate-quotation` (Quotation generation)
- `/api/generate-invoice` (Invoice generation)

## Troubleshooting

### If Playwright still fails:
1. Check the deployment logs for browser installation errors
2. Verify environment variables are set correctly
3. Try manual browser installation via Render shell:
   ```bash
   npx playwright install chromium --with-deps
   ```

### If rate limiting errors persist:
1. Verify `trust proxy` setting in logs
2. Check if requests are coming through Render's load balancer
3. Monitor rate limit headers in responses

### If PDF generation is slow:
1. Consider increasing memory allocation in Render
2. Monitor CPU usage during PDF generation
3. Implement PDF generation queuing if needed

## Performance Considerations

- PDF generation is CPU-intensive
- Consider upgrading to a higher-tier Render plan for better performance
- Implement caching for frequently generated PDFs
- Monitor memory usage during peak loads

## Security Notes

- Trust proxy is configured to trust only the first proxy (Render's load balancer)
- Rate limiting is properly configured to prevent abuse
- Dockerfile uses non-root user for security
- Environment variables should be kept secure in Render dashboard
