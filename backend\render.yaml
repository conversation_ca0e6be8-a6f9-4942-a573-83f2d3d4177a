services:
  - type: web
    name: shans-backend
    env: node
    buildCommand: |
      npm install
      npx playwright install chromium --with-deps
    startCommand: npm start
    envVars:
      - key: NODE_VERSION
        value: 18.20.4
      - key: NPM_CONFIG_PRODUCTION
        value: false
      - key: PLAYWRIGHT_BROWSERS_PATH
        value: /opt/render/.cache/ms-playwright
      - key: NODE_ENV
        value: production
      - key: RENDER
        value: true
      - key: PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD
        value: false
