#!/usr/bin/env node

/**
 * Test script to verify deployment fixes work correctly
 */

const { chromium } = require('playwright');
const express = require('express');
const rateLimit = require('express-rate-limit');

async function testPlaywright() {
  console.log('🧪 Testing Playwright browser launch...');
  
  try {
    const browser = await chromium.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    const page = await browser.newPage();
    await page.setViewportSize({ width: 1200, height: 800 });
    
    // Test basic HTML rendering
    await page.setContent(`
      <html>
        <head><title>Test</title></head>
        <body>
          <h1>Test PDF Generation</h1>
          <p>This is a test document.</p>
        </body>
      </html>
    `);

    // Generate a test PDF
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: { top: '20px', bottom: '20px', left: '20px', right: '20px' },
    });

    await browser.close();
    
    console.log('✅ Playwright test successful');
    console.log(`📄 Generated PDF size: ${pdfBuffer.length} bytes`);
    return true;
  } catch (error) {
    console.error('❌ Playwright test failed:', error);
    return false;
  }
}

function testRateLimit() {
  console.log('🧪 Testing rate limit configuration...');
  
  try {
    const app = express();
    
    // Configure trust proxy
    if (process.env.NODE_ENV === 'production' || process.env.RENDER) {
      app.set('trust proxy', 1);
    } else {
      app.set('trust proxy', false);
    }

    // Configure rate limiter
    const limiter = rateLimit({
      windowMs: 15 * 60 * 1000,
      max: 100,
      message: 'Too many requests',
      standardHeaders: true,
      legacyHeaders: false,
      trustProxy: process.env.NODE_ENV === 'production' || process.env.RENDER ? 1 : false,
      keyGenerator: (req) => {
        if (process.env.NODE_ENV === 'production' || process.env.RENDER) {
          return req.ip || req.connection.remoteAddress || 'unknown';
        }
        return req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
      }
    });

    app.use(limiter);
    
    console.log('✅ Rate limit configuration test successful');
    return true;
  } catch (error) {
    console.error('❌ Rate limit test failed:', error);
    return false;
  }
}

function testEnvironment() {
  console.log('🧪 Testing environment configuration...');
  
  const requiredEnvVars = [
    'NODE_ENV',
    'PLAYWRIGHT_BROWSERS_PATH'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.log(`⚠️  Missing environment variables: ${missingVars.join(', ')}`);
  } else {
    console.log('✅ All required environment variables are set');
  }

  console.log('📊 Environment summary:');
  console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'not set'}`);
  console.log(`   RENDER: ${process.env.RENDER || 'not set'}`);
  console.log(`   PLAYWRIGHT_BROWSERS_PATH: ${process.env.PLAYWRIGHT_BROWSERS_PATH || 'not set'}`);
  console.log(`   PORT: ${process.env.PORT || 'not set'}`);
  
  return missingVars.length === 0;
}

async function runTests() {
  console.log('🚀 Running deployment tests...\n');
  
  const results = {
    environment: testEnvironment(),
    rateLimit: testRateLimit(),
    playwright: await testPlaywright()
  };
  
  console.log('\n📋 Test Results:');
  console.log(`   Environment: ${results.environment ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Rate Limit: ${results.rateLimit ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Playwright: ${results.playwright ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log('\n🎉 All tests passed! Deployment should work correctly.');
    process.exit(0);
  } else {
    console.log('\n⚠️  Some tests failed. Please check the configuration.');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = { testPlaywright, testRateLimit, testEnvironment };
