#!/usr/bin/env node

/**
 * Startup script to ensure Playwright browsers are properly installed
 * and the application can start successfully on Render.com
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

async function startApplication() {
  console.log('🚀 Starting Shans Backend Application...');

  // Check if we're running on Render.com
  const isRender = process.env.RENDER === 'true' || process.env.NODE_ENV === 'production';

  if (isRender) {
    console.log('📦 Running on Render.com - Checking Playwright installation...');

    try {
      // Check if Playwright browsers are installed
      const playwrightPath = process.env.PLAYWRIGHT_BROWSERS_PATH || '/opt/render/.cache/ms-playwright';

      console.log(`🔍 Checking Playwright installation at: ${playwrightPath}`);

      // Try to find any chromium installation
      let chromiumFound = false;
      try {
        if (fs.existsSync(playwrightPath)) {
          const files = fs.readdirSync(playwrightPath);
          chromiumFound = files.some(file => file.includes('chromium'));
          console.log(`📁 Found files in Playwright path: ${files.join(', ')}`);
        }
      } catch (err) {
        console.log('📁 Playwright path does not exist yet');
      }

      if (!chromiumFound) {
        console.log('⚠️  Chromium not found, installing Playwright browsers...');
        execSync('npx playwright install chromium --with-deps', {
          stdio: 'inherit',
          env: { ...process.env, PLAYWRIGHT_BROWSERS_PATH: playwrightPath }
        });
        console.log('✅ Playwright browsers installed successfully');
      } else {
        console.log('✅ Chromium found, skipping installation');
      }

      // Verify Playwright can launch
      console.log('🧪 Testing Playwright browser launch...');
      const { chromium } = require('playwright');

      const browser = await chromium.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--single-process',
          '--disable-gpu'
        ]
      });

      await browser.close();
      console.log('✅ Playwright browser test successful');

    } catch (error) {
      console.error('❌ Error during Playwright setup:', error);
      console.log('🔄 Attempting to reinstall Playwright...');

      try {
        execSync('npm install playwright', { stdio: 'inherit' });
        execSync('npx playwright install chromium --with-deps', { stdio: 'inherit' });
        console.log('✅ Playwright reinstalled successfully');
      } catch (reinstallError) {
        console.error('❌ Failed to reinstall Playwright:', reinstallError);
        console.log('⚠️  Continuing without Playwright verification...');
      }
    }
  }

  // Start the main application
  console.log('🎯 Starting main application...');
  require('./index.js');
}

// Run the startup function
startApplication().catch(error => {
  console.error('❌ Failed to start application:', error);
  process.exit(1);
});
